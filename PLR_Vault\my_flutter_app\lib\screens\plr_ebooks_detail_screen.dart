import 'dart:async';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import '../models/models.dart';
import '../services/content_unlock_service.dart';
import '../services/payment_service.dart';
import '../services/realtime_database_service.dart';
import '../services/user_purchase_tracker.dart';
import 'subscription_screen.dart';

class PLREbooksDetailScreen extends StatefulWidget {
  final ContentItem content;

  const PLREbooksDetailScreen({super.key, required this.content});

  @override
  State<PLREbooksDetailScreen> createState() => _PLREbooksDetailScreenState();
}

class _PLREbooksDetailScreenState extends State<PLREbooksDetailScreen> {
  final ContentUnlockService _unlockService = ContentUnlockService();
  final PaymentService _paymentService = PaymentService();
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();
  final UserPurchaseTracker _purchaseTracker = UserPurchaseTracker();

  bool _isDownloading = false;
  double _downloadProgress = 0.0;
  bool _hasActiveSubscription = false;

  StreamSubscription? _purchaseSubscription;

  @override
  void initState() {
    super.initState();
    _setupPurchaseListener();
    // Check subscription status immediately with fresh data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkSubscriptionStatus();
    });
  }

  @override
  void dispose() {
    _purchaseSubscription?.cancel();
    super.dispose();
  }

  void _setupPurchaseListener() {
    _purchaseSubscription = _paymentService.purchaseStream.listen((
      purchase,
    ) async {
      if (mounted) {
        debugPrint(
          '🔄 Purchase event received in PLR eBooks detail: $purchase',
        );

        // Add small delay to ensure database sync
        await Future.delayed(const Duration(milliseconds: 500));

        if (mounted) {
          await _checkSubscriptionStatus();
        }
      }
    });
  }

  Future<void> _checkSubscriptionStatus() async {
    try {
      // Clear cache to get fresh data
      _purchaseTracker.clearCache();

      final hasSubscription = await _purchaseTracker.hasActiveSubscription();

      if (mounted) {
        setState(() {
          _hasActiveSubscription = hasSubscription;
        });
        debugPrint('💎 PLR eBooks subscription status: $hasSubscription');
      }
    } catch (e) {
      debugPrint('❌ Error checking subscription status: $e');
    }
  }

  Future<void> _downloadWithAd() async {
    if (_isDownloading) return;

    // If user has active subscription, download directly
    if (_hasActiveSubscription) {
      await _downloadContent();
      return;
    }

    // For videos, download directly without ad
    if (widget.content.isVideo) {
      await _downloadContent();
      return;
    }

    setState(() {
      _isDownloading = true;
    });

    try {
      // Show ad first
      final adResult = await _unlockService.showAdForDownload(widget.content);

      if (mounted) {
        if (adResult.success) {
          // Ad was successful, now download
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(adResult.message),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );

          // Start download immediately after successful ad
          await _downloadContent();
        } else {
          setState(() {
            _isDownloading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(adResult.message),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error showing ad: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _downloadContent() async {
    // This method is called after ad is shown or for videos
    if (!mounted) return;

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      // For regular PLR eBooks content, use real progress
      final result = await _unlockService.downloadContent(
        content: widget.content,
        onProgress: (progress) {
          if (mounted) {
            setState(() {
              _downloadProgress = progress;
            });
          }
        },
      );

      if (mounted) {
        setState(() {
          _isDownloading = false;
          _downloadProgress = 1.0;
        });
      }

      await _handleDownloadResult(result);
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _downloadProgress = 0.0;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download failed: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  IconData _getFileTypeIcon(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.archive;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
        return Icons.audio_file;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return Icons.image;
      default:
        return Icons.insert_drive_file;
    }
  }

  Future<void> _handleDownloadResult(dynamic result) async {
    try {
      if (result.success) {
        // Track download in database
        try {
          final download = UserDownload(
            contentId: widget.content.id,
            title: widget.content.title,
            fileName: widget.content.fileName,
            category: widget.content.category,
            fileUrl: widget.content.fileUrl,
            fileSize: widget.content.fileSize,
            downloadedAt: DateTime.now(),
            localPath: result.localPath ?? '',
            isDownloaded: true,
          );
          await _dbService.addUserDownload(download);
          debugPrint('✅ Download tracked in database');
        } catch (e) {
          debugPrint('⚠️ Failed to track download: $e');
        }

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ File downloaded successfully!'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        }

        // Open the downloaded file immediately
        if (result.localPath != null) {
          try {
            debugPrint('🔍 Attempting to open file: ${result.localPath!}');

            // Try to open the file
            final openResult = await OpenFilex.open(result.localPath!);

            debugPrint(
              '📱 File open result: ${openResult.type} - ${openResult.message}',
            );

            if (mounted) {
              if (openResult.type == ResultType.done) {
                // File opened successfully
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('📱 File opened successfully!'),
                    backgroundColor: Colors.blue,
                    duration: Duration(seconds: 2),
                  ),
                );
              } else if (openResult.type == ResultType.noAppToOpen) {
                // No app to open this file type
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      '📁 File saved to: ${result.localPath!}\n\nNo app found to open this file type. You can find it in your file manager.',
                    ),
                    backgroundColor: Colors.orange,
                    duration: const Duration(seconds: 4),
                  ),
                );
              } else {
                // Other error
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      '📁 File saved to: ${result.localPath!}\n\n${openResult.message}',
                    ),
                    backgroundColor: Colors.blue,
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            }
          } catch (e) {
            debugPrint('❌ Error opening file: $e');
            // If opening fails, show file location
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    '📁 File saved to: ${result.localPath!}\n\nTap to view in file manager.',
                  ),
                  backgroundColor: Colors.blue,
                  duration: const Duration(seconds: 4),
                  action: SnackBarAction(
                    label: 'Open Folder',
                    textColor: Colors.white,
                    onPressed: () => _openFileLocation(result.localPath!),
                  ),
                ),
              );
            }
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.errorMessage ?? 'Download failed'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to handle download result: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Open file location in file manager
  Future<void> _openFileLocation(String filePath) async {
    try {
      // Extract directory path from file path
      final directory = filePath.substring(0, filePath.lastIndexOf('/'));
      debugPrint('🗂️ Opening directory: $directory');

      // Try to open the directory
      final result = await OpenFilex.open(directory);

      if (result.type != ResultType.done) {
        // If directory opening fails, show the full path
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('📁 File location: $filePath'),
              backgroundColor: Colors.blue,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error opening file location: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('📁 File location: $filePath'),
            backgroundColor: Colors.blue,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'PLR eBook Details',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
        backgroundColor: Colors.blue,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24.0),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // File type icon
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      _getFileTypeIcon(widget.content.fileExtension),
                      color: Colors.white,
                      size: 48,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.content.title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.content.category,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),

            // Content Details
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Description
                  if (widget.content.description.isNotEmpty) ...[
                    const Text(
                      'Description',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.content.description,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[700],
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],

                  // Subscription Card for PLR EBOOKS
                  Card(
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: InkWell(
                      onTap: _hasActiveSubscription
                          ? null
                          : () async {
                              final result = await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const SubscriptionScreen(),
                                ),
                              );

                              // Refresh subscription status when returning
                              if (result == true) {
                                debugPrint(
                                  '🔄 Subscription purchased, refreshing status...',
                                );
                                _purchaseTracker.clearCache();
                                await _checkSubscriptionStatus();
                              }
                            },
                      borderRadius: BorderRadius.circular(16),
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.diamond_outlined,
                              color: Colors.blue[700],
                              size: 28,
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _hasActiveSubscription
                                        ? 'Premium Member'
                                        : 'Get Premium Access',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.blue[700],
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    _hasActiveSubscription
                                        ? 'Enjoy ad-free downloads'
                                        : 'Subscribe for ad-free downloads',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (!_hasActiveSubscription)
                              Icon(
                                Icons.arrow_forward_ios,
                                color: Colors.blue[700],
                                size: 16,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 30),

                  // Download Button
                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton(
                      onPressed: _isDownloading ? null : _downloadWithAd,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 4,
                      ),
                      child: _isDownloading
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    value: _downloadProgress > 0
                                        ? _downloadProgress
                                        : null,
                                    strokeWidth: 2,
                                    valueColor:
                                        const AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text(
                                  _downloadProgress > 0
                                      ? 'Downloading ${(_downloadProgress * 100).toInt()}%'
                                      : 'Downloading...',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.download, size: 24),
                                const SizedBox(width: 8),
                                Text(
                                  _hasActiveSubscription
                                      ? 'Download Now'
                                      : 'Watch Ad & Download',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // File Info
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline, color: Colors.grey[600]),
                            const SizedBox(width: 8),
                            const Text(
                              'File Information',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('File Type:'),
                            Text(
                              widget.content.fileExtension.toUpperCase(),
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('File Size:'),
                            Text(
                              widget.content.formattedFileSize,
                              style: const TextStyle(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
