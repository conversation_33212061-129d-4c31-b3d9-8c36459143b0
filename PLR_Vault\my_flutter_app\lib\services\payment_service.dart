import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import '../models/subscription_model.dart';
import '../models/purchase_model.dart';
import 'auth_service.dart';

class PaymentService {
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  final AuthService _authService = AuthService();
  final RealtimeDatabaseService _dbService = RealtimeDatabaseService();

  // Product IDs - These must match your Google Play Console setup
  static const String monthlyAdsSubscription =
      'monthlyadssubscription'; // $9.99/month for ad-free PLR EBOOKS (MATCHES GOOGLE PLAY CONSOLE)
  static const String plrBundleProduct =
      'plrbundle'; // $9.99 consumable for ANY PLR Bundle item
  static const String mrrCoursesProduct =
      'mrrcourses'; // $49.99 consumable for ANY MRR Course item

  // Stream controllers
  final StreamController<List<PurchaseDetails>> _purchaseUpdatedController =
      StreamController<List<PurchaseDetails>>.broadcast();

  // Track current purchase content for simple reliable tracking
  String? _currentPurchaseContentId;
  String? _currentPurchaseContentTitle;

  late StreamSubscription<List<PurchaseDetails>> _subscription;
  bool _isInitialized = false;
  bool _isAvailable = false;
  List<ProductDetails> _products = [];
  final List<PurchaseDetails> _purchases = [];

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isAvailable => _isAvailable;
  List<ProductDetails> get products => _products;
  List<PurchaseDetails> get purchases => _purchases;
  Stream<List<PurchaseDetails>> get purchaseStream =>
      _purchaseUpdatedController.stream;

  /// Initialize the payment service
  Future<void> initialize() async {
    try {
      _isAvailable = await _inAppPurchase.isAvailable();

      if (!_isAvailable) {
        debugPrint('❌ In-app purchases not available');
        return;
      }

      // Listen to purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => _subscription.cancel(),
        onError: (error) => debugPrint('❌ Purchase stream error: $error'),
      );

      // Load products
      await _loadProducts();

      // Restore previous purchases
      await _restorePurchases();

      _isInitialized = true;
      debugPrint('✅ Payment service initialized successfully');
    } catch (e) {
      debugPrint('❌ Failed to initialize payment service: $e');
      _isInitialized = false;
    }
  }

  /// Load available products from Google Play
  Future<void> _loadProducts([Set<String>? additionalProductIds]) async {
    try {
      Set<String> productIds = {
        monthlyAdsSubscription,
        plrBundleProduct,
        mrrCoursesProduct,
      };

      // Add any additional product IDs (for specific content items)
      if (additionalProductIds != null) {
        productIds.addAll(additionalProductIds);
      }

      final ProductDetailsResponse response = await _inAppPurchase
          .queryProductDetails(productIds);

      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('❌ Products not found: ${response.notFoundIDs}');
      }

      _products = response.productDetails;
      debugPrint('✅ Loaded ${_products.length} products');

      for (var product in _products) {
        debugPrint(
          '📦 Product: ${product.id} - ${product.title} - ${product.price}',
        );
      }
    } catch (e) {
      debugPrint('❌ Failed to load products: $e');
    }
  }

  /// Handle purchase updates
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      _handlePurchase(purchaseDetails);
    }
    _purchaseUpdatedController.add(purchaseDetailsList);
  }

  /// Handle individual purchase
  Future<void> _handlePurchase(PurchaseDetails purchaseDetails) async {
    try {
      if (purchaseDetails.status == PurchaseStatus.purchased) {
        try {
          // Verify and save the purchase to database FIRST
          await _verifyAndSavePurchase(purchaseDetails);

          // Update local state immediately
          await _updateLocalPurchaseState(purchaseDetails);

          // CRITICAL: Complete the purchase ONLY after successful save
          if (purchaseDetails.pendingCompletePurchase) {
            await _inAppPurchase.completePurchase(purchaseDetails);
          }
        } catch (saveError) {
          // Don't complete the purchase if save failed
          _purchaseUpdatedController.add([purchaseDetails]);
          return;
        }
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        _purchaseUpdatedController.add([purchaseDetails]);
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        _purchaseUpdatedController.add([purchaseDetails]);
      } else if (purchaseDetails.status == PurchaseStatus.pending) {
        _purchaseUpdatedController.add([purchaseDetails]);
      } else if (purchaseDetails.status == PurchaseStatus.restored) {
        // Handle restored purchases
        await _verifyAndSavePurchase(purchaseDetails);
        await _updateLocalPurchaseState(purchaseDetails);

        // Complete restored purchase
        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
        }
      }
    } catch (e) {
      _purchaseUpdatedController.add([purchaseDetails]);
    }
  }

  /// Verify and save purchase to database
  Future<void> _verifyAndSavePurchase(PurchaseDetails purchaseDetails) async {
    try {
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      if (purchaseDetails.productID == monthlyAdsSubscription) {
        // Handle subscription
        await _saveUserSubscription(userId, purchaseDetails);
      } else if (purchaseDetails.productID == plrBundleProduct) {
        // Handle PLR Bundle purchase
        if (_currentPurchaseContentId == null ||
            _currentPurchaseContentId!.isEmpty) {
          throw Exception(
            'No content ID available for PLR Bundle purchase tracking',
          );
        }

        await _saveContentPurchase(
          userId,
          purchaseDetails,
          contentId: _currentPurchaseContentId,
          contentTitle: _currentPurchaseContentTitle,
          contentType: 'plr_bundle',
        );
      } else if (purchaseDetails.productID == mrrCoursesProduct) {
        // Handle MRR Courses purchase
        if (_currentPurchaseContentId == null ||
            _currentPurchaseContentId!.isEmpty) {
          throw Exception(
            'No content ID available for MRR Course purchase tracking',
          );
        }

        await _saveContentPurchase(
          userId,
          purchaseDetails,
          contentId: _currentPurchaseContentId,
          contentTitle: _currentPurchaseContentTitle,
          contentType: 'mrr_course',
        );
      }

      // Clear the current purchase tracking ONLY after successful save
      _currentPurchaseContentId = null;
      _currentPurchaseContentTitle = null;
    } catch (e) {
      rethrow;
    }
  }

  /// Save user subscription status to database
  Future<void> _saveUserSubscription(
    String userId,
    PurchaseDetails purchaseDetails,
  ) async {
    try {
      final userEmail = _authService.currentUser?.email ?? '';
      final now = DateTime.now();
      final endDate = now.add(const Duration(days: 30)); // 30 days subscription

      // Save subscription status in user's record with proper structure
      await _dbService.setUserSubscriptionStatus(
        userId,
        userEmail,
        isSubscribed: true,
        subscriptionStartDate: now,
        subscriptionEndDate: endDate,
        transactionId: purchaseDetails.purchaseID ?? '',
      );

      // Verify the subscription was saved correctly
      final hasActive = await _dbService.hasActiveUserSubscription(userId);
      if (!hasActive) {
        throw Exception('Subscription verification failed');
      }
    } catch (e) {
      throw Exception('Failed to save user subscription: $e');
    }
  }

  /// Save individual content purchase to database
  Future<void> _saveContentPurchase(
    String userId,
    PurchaseDetails purchaseDetails, {
    String? contentId,
    String? contentTitle,
    required String contentType,
  }) async {
    try {
      final userEmail = _authService.currentUser?.email ?? '';

      if (contentId == null || contentId.isEmpty) {
        throw Exception('Content ID is required for purchase tracking');
      }

      // Save individual content purchase in user's purchases
      await _dbService.saveUserContentPurchase(
        userId,
        userEmail,
        contentId: contentId,
        contentTitle: contentTitle ?? 'Unknown Content',
        contentType: contentType,
        purchaseDate: DateTime.now(),
        transactionId: 'PURCHASE_${DateTime.now().millisecondsSinceEpoch}',
        price: contentType == 'mrr_course' ? 49.99 : 9.99,
      );

      // Wait a moment for database to sync
      await Future.delayed(const Duration(milliseconds: 500));

      // Verify the purchase was saved correctly
      final hasPurchased = await _dbService.hasUserPurchasedContent(
        userId,
        contentId,
      );
      if (!hasPurchased) {
        throw Exception('Purchase verification failed - not found in database');
      }
    } catch (e) {
      throw Exception('Failed to save content purchase: $e');
    }
  }

  /// Update local purchase state immediately after successful purchase
  Future<void> _updateLocalPurchaseState(
    PurchaseDetails purchaseDetails,
  ) async {
    try {
      debugPrint('🔄 Updating local purchase state...');

      // Use the stored content ID from current purchase tracking
      final contentId = _currentPurchaseContentId;

      // Force refresh of purchase status
      if (purchaseDetails.productID == monthlyAdsSubscription) {
        // Subscription purchased - user gets Diamond status
        debugPrint(
          '💎 Monthly subscription activated - Diamond status granted',
        );
      } else if (purchaseDetails.productID == plrBundleProduct) {
        // PLR Bundle purchase
        debugPrint('🎯 PLR Bundle access granted for: $contentId');
      } else if (purchaseDetails.productID == mrrCoursesProduct) {
        // MRR Courses purchase
        debugPrint('🎯 MRR Course access granted for: $contentId');
      }

      // Trigger UI refresh by notifying listeners
      _purchaseUpdatedController.add([purchaseDetails]);

      debugPrint('✅ Local purchase state updated');
    } catch (e) {
      debugPrint('❌ Failed to update local purchase state: $e');
    }
  }

  /// Restore previous purchases on app start
  Future<void> _restorePurchases() async {
    try {
      debugPrint('🔄 Restoring previous purchases...');

      // Restore purchases (this triggers the purchase stream with past purchases)
      await _inAppPurchase.restorePurchases();

      debugPrint('✅ Purchase restoration initiated');
    } catch (e) {
      debugPrint('❌ Failed to restore purchases: $e');
    }
  }

  /// Purchase PLR EBOOKS monthly subscription ($9.99/month for ad-free access)
  Future<bool> purchasePlrEbooksSubscription() async {
    try {
      if (!_isAvailable) {
        debugPrint('❌ In-app purchases not available');
        return false;
      }

      debugPrint(
        '🔍 Looking for subscription product: $monthlyAdsSubscription',
      );
      debugPrint(
        '🔍 Available products: ${_products.map((p) => p.id).toList()}',
      );

      final ProductDetails? productDetails = _products
          .where((product) => product.id == monthlyAdsSubscription)
          .firstOrNull;

      if (productDetails == null) {
        debugPrint('❌ Monthly ads subscription product not found');
        debugPrint(
          '❌ Available products: ${_products.map((p) => '${p.id}: ${p.title}').join(', ')}',
        );
        return false;
      }

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      debugPrint('🛒 Initiating PLR EBOOKS subscription purchase...');
      debugPrint(
        '🛒 Product: ${productDetails.title} - ${productDetails.price}',
      );
      // Use buyNonConsumable for subscriptions (Google Play handles subscription logic)
      final bool success = await _inAppPurchase.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      debugPrint('🛒 Purchase result: $success');
      return success;
    } catch (e) {
      debugPrint('❌ Failed to purchase PLR EBOOKS subscription: $e');
      return false;
    }
  }

  /// Purchase PLR Bundle ($9.99 consumable purchase - allows multiple purchases)
  Future<bool> purchasePlrBundle({
    String? specificContentId,
    String? contentTitle,
  }) async {
    try {
      if (!_isAvailable) {
        debugPrint('❌ In-app purchases not available');
        return false;
      }

      if (specificContentId == null) {
        debugPrint('❌ Specific content ID required for PLR Bundle purchase');
        return false;
      }

      // Store the content details for purchase tracking
      _currentPurchaseContentId = specificContentId;
      _currentPurchaseContentTitle = contentTitle ?? 'PLR Bundle Content';

      final ProductDetails? productDetails = _products
          .where((product) => product.id == plrBundleProduct)
          .firstOrNull;

      if (productDetails == null) {
        debugPrint('❌ PLR Bundle product not found: $plrBundleProduct');
        debugPrint(
          '❌ Make sure this product ID is registered in Google Play Console',
        );
        return false;
      }

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      debugPrint('🛒 Initiating PLR Bundle purchase...');
      debugPrint(
        '🛒 Product: ${productDetails.title} - ${productDetails.price}',
      );
      debugPrint('🛒 Content: $specificContentId');

      // Use buyConsumable with autoConsume to allow multiple purchases of the same product ID
      final bool success = await _inAppPurchase.buyConsumable(
        purchaseParam: purchaseParam,
        autoConsume:
            true, // ✅ This makes it consumable - critical for independent purchases!
      );

      // Content ID and title are already stored in _currentPurchaseContentId and _currentPurchaseContentTitle
      if (success) {
        debugPrint('🔍 PLR Bundle purchase initiated successfully');
        debugPrint('🔍 Content will be tracked: $specificContentId');
      }

      debugPrint('🛒 Purchase result: $success');
      return success;
    } catch (e) {
      debugPrint('❌ Failed to purchase PLR Bundle: $e');
      return false;
    }
  }

  /// Purchase MRR Courses ($49.99 consumable purchase - allows multiple purchases)
  Future<bool> purchaseMrrCourses({
    String? specificContentId,
    String? contentTitle,
  }) async {
    try {
      if (!_isAvailable) {
        debugPrint('❌ In-app purchases not available');
        return false;
      }

      if (specificContentId == null) {
        debugPrint('❌ Specific content ID required for MRR Course purchase');
        return false;
      }

      // Store the content details for purchase tracking
      _currentPurchaseContentId = specificContentId;
      _currentPurchaseContentTitle = contentTitle ?? 'MRR Course Content';

      final ProductDetails? productDetails = _products
          .where((product) => product.id == mrrCoursesProduct)
          .firstOrNull;

      if (productDetails == null) {
        debugPrint('❌ MRR Course product not found: $mrrCoursesProduct');
        debugPrint(
          '❌ Make sure this product ID is registered in Google Play Console',
        );
        return false;
      }

      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );

      debugPrint('🛒 Initiating MRR Course purchase...');
      debugPrint(
        '🛒 Product: ${productDetails.title} - ${productDetails.price}',
      );
      debugPrint('🛒 Content: $specificContentId');

      // Use buyConsumable with autoConsume to allow multiple purchases of the same product ID
      final bool success = await _inAppPurchase.buyConsumable(
        purchaseParam: purchaseParam,
        autoConsume:
            true, // ✅ This makes it consumable - critical for independent purchases!
      );

      // Content ID and title are already stored in _currentPurchaseContentId and _currentPurchaseContentTitle
      if (success) {
        debugPrint('🔍 MRR Course purchase initiated successfully');
        debugPrint('🔍 Content will be tracked: $specificContentId');
      }

      debugPrint('🛒 Purchase result: $success');
      return success;
    } catch (e) {
      debugPrint('❌ Failed to purchase MRR Courses: $e');
      return false;
    }
  }

  /// Check if user has active monthly ads subscription
  Future<bool> hasActiveSubscription() async {
    try {
      debugPrint('🔍 Checking active subscription status...');
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) {
        debugPrint('❌ No user logged in');
        return false;
      }

      final hasActive = await _dbService.hasActiveUserSubscription(userId);
      debugPrint('✅ Active subscription status: $hasActive');

      // Additional logging for debugging
      if (hasActive) {
        debugPrint('💎 User has active subscription - Diamond status granted');
      } else {
        debugPrint('❌ User does not have active subscription');
      }

      return hasActive;
    } catch (e) {
      debugPrint('❌ Failed to check subscription status: $e');
      return false;
    }
  }

  /// Get subscription details for UI display
  Future<Map<String, dynamic>?> getSubscriptionDetails() async {
    try {
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) return null;

      // Use the existing database service method to check subscription
      final hasActive = await _dbService.hasActiveUserSubscription(userId);
      if (!hasActive) return null;

      // For now, return basic info - we can enhance this later if needed
      return {
        'isActive': true,
        'endDate': DateTime.now().add(
          const Duration(days: 30),
        ), // Default 30 days
        'startDate': DateTime.now(),
        'transactionId': 'subscription_active',
      };
    } catch (e) {
      debugPrint('❌ Failed to get subscription details: $e');
      return null;
    }
  }

  /// Check if user has active monthly ads subscription (alias for compatibility)
  Future<bool> hasActiveMonthlySubscription() async {
    return hasActiveSubscription();
  }

  /// Check if user has purchased specific PLR Bundle content
  Future<bool> hasPurchasedSpecificPlrBundle(String contentId) async {
    try {
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) return false;

      return await _dbService.hasUserPurchasedContent(userId, contentId);
    } catch (e) {
      return false;
    }
  }

  /// Check if user has purchased specific MRR Course content
  Future<bool> hasPurchasedSpecificMrrCourse(String contentId) async {
    try {
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) return false;

      return await _dbService.hasUserPurchasedContent(userId, contentId);
    } catch (e) {
      return false;
    }
  }

  /// Check if user has purchased PLR Bundle access (legacy method for compatibility)
  Future<bool> hasPurchasedPlrBundle() async {
    try {
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) return false;

      return await _dbService.hasPurchasedContent(userId, 'plr_bundle_content');
    } catch (e) {
      debugPrint('❌ Failed to check PLR Bundle purchase status: $e');
      return false;
    }
  }

  /// Check if user has purchased MRR Courses access
  Future<bool> hasPurchasedMrrCourses() async {
    try {
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) return false;

      return await _dbService.hasPurchasedContent(
        userId,
        'mrr_courses_content',
      );
    } catch (e) {
      debugPrint('❌ Failed to check MRR Courses purchase status: $e');
      return false;
    }
  }

  /// Get user's active subscription
  Future<SubscriptionModel?> getActiveSubscription() async {
    try {
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) return null;

      return await _dbService.getActiveSubscription(userId);
    } catch (e) {
      debugPrint('❌ Failed to get active subscription: $e');
      return null;
    }
  }

  /// Get all user purchases for comprehensive tracking
  Future<List<PurchaseModel>> getUserPurchases() async {
    try {
      debugPrint('🔍 Getting all user purchases...');
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) {
        debugPrint('❌ No user logged in');
        return [];
      }

      final purchases = await _dbService.getUserActivePurchases(userId);
      debugPrint('✅ Retrieved ${purchases.length} active user purchases');
      return purchases;
    } catch (e) {
      debugPrint('❌ Failed to get user purchases: $e');
      return [];
    }
  }

  /// Get user's purchased PLR bundles
  Future<List<PurchaseModel>> getUserPlrBundles() async {
    try {
      debugPrint('🔍 Getting user PLR bundles...');
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) {
        debugPrint('❌ No user logged in');
        return [];
      }

      final plrBundles = await _dbService.getUserPurchasesByType(
        userId,
        'plr_bundle',
      );
      debugPrint('✅ Retrieved ${plrBundles.length} PLR bundle purchases');
      return plrBundles;
    } catch (e) {
      debugPrint('❌ Failed to get user PLR bundles: $e');
      return [];
    }
  }

  /// Get user's purchased MRR courses
  Future<List<PurchaseModel>> getUserMrrCourses() async {
    try {
      debugPrint('🔍 Getting user MRR courses...');
      final String? userId = _authService.currentUser?.uid;
      if (userId == null) {
        debugPrint('❌ No user logged in');
        return [];
      }

      final mrrCourses = await _dbService.getUserPurchasesByType(
        userId,
        'mrr_course',
      );
      debugPrint('✅ Retrieved ${mrrCourses.length} MRR course purchases');
      return mrrCourses;
    } catch (e) {
      debugPrint('❌ Failed to get user MRR courses: $e');
      return [];
    }
  }

  /// Restore purchases
  Future<void> restorePurchases() async {
    try {
      debugPrint('🔄 Restoring purchases...');
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      debugPrint('❌ Failed to restore purchases: $e');
    }
  }

  /// Get available products for debugging
  List<ProductDetails> get availableProducts => _products;

  /// Comprehensive payment system validation for Google Play Store
  Future<Map<String, dynamic>> validatePaymentSetup() async {
    final validation = <String, dynamic>{
      'isAvailable': _isAvailable,
      'productsLoaded': _products.length,
      'hasSubscriptionProduct': false,
      'hasInAppProduct': false,
      'subscriptionPrice': null,
      'inAppPrice': null,
      'errors': <String>[],
    };

    try {
      // Check if subscription product exists
      final subscriptionProduct = _products
          .where((p) => p.id == monthlyAdsSubscription)
          .firstOrNull;

      if (subscriptionProduct != null) {
        validation['hasSubscriptionProduct'] = true;
        validation['subscriptionPrice'] = subscriptionProduct.price;
      } else {
        validation['errors'].add(
          'Subscription product not found: $monthlyAdsSubscription',
        );
      }

      // Check if PLR Bundle product exists
      final plrBundleInApp = _products
          .where((p) => p.id == plrBundleProduct)
          .firstOrNull;

      if (plrBundleInApp != null) {
        validation['hasPlrBundleProduct'] = true;
        validation['plrBundlePrice'] = plrBundleInApp.price;
        validation['plrBundleSystem'] =
            'Single consumable product ID for all PLR bundles';
      } else {
        validation['errors'].add(
          'PLR Bundle product not found: $plrBundleProduct',
        );
        validation['hasPlrBundleProduct'] = false;
      }

      // Check if MRR Courses product exists
      final mrrCoursesInApp = _products
          .where((p) => p.id == mrrCoursesProduct)
          .firstOrNull;

      if (mrrCoursesInApp != null) {
        validation['hasMrrCoursesProduct'] = true;
        validation['mrrCoursesPrice'] = mrrCoursesInApp.price;
        validation['mrrCoursesSystem'] =
            'Single consumable product ID for all MRR courses';
      } else {
        validation['errors'].add(
          'MRR Courses product not found: $mrrCoursesProduct',
        );
        validation['hasMrrCoursesProduct'] = false;
      }

      validation['isValid'] =
          validation['hasSubscriptionProduct'] &&
          validation['hasPlrBundleProduct'] &&
          validation['hasMrrCoursesProduct'] &&
          (validation['errors'] as List).isEmpty;
    } catch (e) {
      validation['errors'].add('Validation error: $e');
      validation['isValid'] = false;
    }

    return validation;
  }

  /// Test payment system end-to-end for Google Play Store readiness
  Future<Map<String, dynamic>> testPaymentSystem() async {
    final Map<String, dynamic> testResults = {
      'isReady': false,
      'tests': <String, bool>{},
      'errors': <String>[],
      'warnings': <String>[],
    };

    try {
      // Test 1: Initialize payment service
      testResults['tests']['initialization'] = _isInitialized;
      if (!_isInitialized) {
        testResults['errors'].add('Payment service not initialized');
      }

      // Test 2: Check availability
      testResults['tests']['availability'] = _isAvailable;
      if (!_isAvailable) {
        testResults['errors'].add('In-app purchases not available');
      }

      // Test 3: Verify products loaded
      testResults['tests']['products_loaded'] = _products.isNotEmpty;
      if (_products.isEmpty) {
        testResults['errors'].add('No products loaded from Google Play');
      }

      // Test 4: Check subscription product matches Google Play Console
      final hasSubscription = _products.any(
        (p) => p.id == monthlyAdsSubscription,
      );
      testResults['tests']['subscription_product'] = hasSubscription;
      if (!hasSubscription) {
        testResults['errors'].add(
          'Subscription product not found: $monthlyAdsSubscription',
        );
      }

      // Test 5: Check PLR Bundle product matches Google Play Console
      final hasPlrBundle = _products.any((p) => p.id == plrBundleProduct);
      testResults['tests']['plr_bundle_product'] = hasPlrBundle;
      if (!hasPlrBundle) {
        testResults['errors'].add(
          'PLR Bundle product not found: $plrBundleProduct',
        );
      }

      // Test 6: Check MRR Courses product matches Google Play Console
      final hasMrrCourses = _products.any((p) => p.id == mrrCoursesProduct);
      testResults['tests']['mrr_courses_product'] = hasMrrCourses;
      if (!hasMrrCourses) {
        testResults['errors'].add(
          'MRR Courses product not found: $mrrCoursesProduct',
        );
      }

      testResults['product_info'] = {
        'plr_bundle_id': plrBundleProduct,
        'mrr_courses_id': mrrCoursesProduct,
        'system': 'Single consumable product ID per category',
      };

      // Test 6: Database connectivity
      try {
        final userId = _authService.currentUser?.uid;
        if (userId != null) {
          await _dbService.hasActiveSubscription(userId, 'monthly_ads_skip');
          testResults['tests']['database_connectivity'] = true;
        } else {
          testResults['tests']['database_connectivity'] = false;
          testResults['warnings'].add('No user logged in for database test');
        }
      } catch (e) {
        testResults['tests']['database_connectivity'] = false;
        testResults['errors'].add('Database connectivity failed: $e');
      }

      // Overall readiness for Google Play Store
      final allTestsPassed = testResults['tests'].values.every(
        (test) => test == true,
      );
      testResults['isReady'] =
          allTestsPassed && (testResults['errors'] as List).isEmpty;

      return testResults;
    } catch (e) {
      testResults['errors'].add('Test system failed: $e');
      return testResults;
    }
  }

  /// Force refresh purchase status (call after successful purchase)
  Future<void> refreshPurchaseStatus() async {
    try {
      debugPrint('🔄 Refreshing purchase status...');
      await _restorePurchases();
      debugPrint('✅ Purchase status refreshed');
    } catch (e) {
      debugPrint('❌ Failed to refresh purchase status: $e');
    }
  }

  /// Comprehensive payment system test
  Future<Map<String, dynamic>> runComprehensiveTest() async {
    final testResults = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, dynamic>{},
      'errors': <String>[],
      'warnings': <String>[],
      'summary': <String, dynamic>{},
    };

    try {
      debugPrint('🧪 Running comprehensive payment system test...');

      // Test 1: Service initialization
      testResults['tests']['service_initialized'] = _isInitialized;
      if (!_isInitialized) {
        testResults['errors'].add('Payment service not initialized');
      }

      // Test 2: In-app purchase availability
      testResults['tests']['iap_available'] = _isAvailable;
      if (!_isAvailable) {
        testResults['errors'].add('In-app purchases not available');
      }

      // Test 3: Product loading
      testResults['tests']['products_loaded'] = _products.isNotEmpty;
      testResults['product_count'] = _products.length;
      testResults['loaded_products'] = _products
          .map((p) => {'id': p.id, 'title': p.title, 'price': p.price})
          .toList();

      // Test 4: Required products check
      final requiredProducts = [
        monthlyAdsSubscription,
        plrBundleProduct,
        mrrCoursesProduct,
      ];
      final missingProducts = <String>[];

      for (final productId in requiredProducts) {
        final exists = _products.any((p) => p.id == productId);
        testResults['tests']['product_$productId'] = exists;
        if (!exists) {
          missingProducts.add(productId);
        }
      }

      if (missingProducts.isNotEmpty) {
        testResults['errors'].add(
          'Missing products: ${missingProducts.join(', ')}',
        );
      }

      // Test 5: Database connectivity
      try {
        final userId = _authService.currentUser?.uid;
        if (userId != null) {
          await _dbService.getUserPurchases(userId);
          testResults['tests']['database_connectivity'] = true;
        } else {
          testResults['warnings'].add('No user logged in for database test');
          testResults['tests']['database_connectivity'] = false;
        }
      } catch (e) {
        testResults['tests']['database_connectivity'] = false;
        testResults['errors'].add('Database connectivity failed: $e');
      }

      // Test 6: Content ID generation
      final testContentId1 = _generateTestContentId(
        'plr_bundle',
        'Test PLR Bundle',
        'PLR Bundle',
        '123',
      );
      final testContentId2 = _generateTestContentId(
        'mrr_course',
        'Test MRR Course',
        'MRR Video Courses',
        '456',
      );

      testResults['tests']['content_id_generation'] =
          testContentId1.isNotEmpty && testContentId2.isNotEmpty;
      testResults['sample_content_ids'] = {
        'plr_bundle': testContentId1,
        'mrr_course': testContentId2,
      };

      // Test 7: Purchase stream
      testResults['tests']['purchase_stream_active'] =
          !_purchaseUpdatedController.isClosed;

      // Summary
      final totalTests = testResults['tests'].length;
      final passedTests = (testResults['tests'] as Map).values
          .where((v) => v == true)
          .length;
      final errorCount = (testResults['errors'] as List).length;
      final warningCount = (testResults['warnings'] as List).length;

      testResults['summary'] = {
        'total_tests': totalTests,
        'passed_tests': passedTests,
        'failed_tests': totalTests - passedTests,
        'error_count': errorCount,
        'warning_count': warningCount,
        'success_rate': totalTests > 0
            ? (passedTests / totalTests * 100).toStringAsFixed(1)
            : '0.0',
        'overall_status': errorCount == 0 ? 'PASS' : 'FAIL',
      };

      debugPrint('✅ Comprehensive test completed');
      debugPrint('📊 Results: ${passedTests}/${totalTests} tests passed');

      return testResults;
    } catch (e) {
      testResults['errors'].add('Test system failed: $e');
      return testResults;
    }
  }

  /// Generate test content ID (helper for testing)
  String _generateTestContentId(
    String type,
    String title,
    String category,
    String id,
  ) {
    final cleanTitle = title
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), '')
        .replaceAll(RegExp(r'\s+'), '_');
    final cleanCategory = category
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), '')
        .replaceAll(RegExp(r'\s+'), '_');

    return '${type}_${cleanCategory}_${cleanTitle}_$id';
  }

  /// Quick diagnostic for payment issues
  Future<Map<String, dynamic>> quickDiagnostic() async {
    final diagnostic = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'issues': <String>[],
      'warnings': <String>[],
      'status': 'CHECKING',
    };

    try {
      // Check 1: Service initialization
      diagnostic['service_initialized'] = _isInitialized;
      if (!_isInitialized) {
        diagnostic['issues'].add('Payment service not initialized');
      }

      // Check 2: Product availability
      diagnostic['products_loaded'] = _products.length;
      if (_products.isEmpty) {
        diagnostic['issues'].add('No products loaded from Google Play');
      }

      // Check 3: Required products
      final requiredProducts = [
        monthlyAdsSubscription,
        plrBundleProduct,
        mrrCoursesProduct,
      ];
      final missingProducts = <String>[];
      for (final productId in requiredProducts) {
        if (!_products.any((p) => p.id == productId)) {
          missingProducts.add(productId);
        }
      }
      diagnostic['missing_products'] = missingProducts;
      if (missingProducts.isNotEmpty) {
        diagnostic['issues'].add(
          'Missing products: ${missingProducts.join(', ')}',
        );
      }

      // Check 4: User authentication
      final user = _authService.currentUser;
      diagnostic['user_authenticated'] = user != null;
      diagnostic['user_email'] = user?.email;
      if (user == null) {
        diagnostic['issues'].add('No user logged in');
      }

      // Check 5: In-app purchase availability
      diagnostic['iap_available'] = _isAvailable;
      if (!_isAvailable) {
        diagnostic['issues'].add(
          'In-app purchases not available on this device',
        );
      }

      // Determine overall status
      if (diagnostic['issues'].isEmpty) {
        diagnostic['status'] = 'READY';
      } else if (diagnostic['issues'].length <= 2) {
        diagnostic['status'] = 'WARNING';
      } else {
        diagnostic['status'] = 'ERROR';
      }

      debugPrint('🔍 Quick Diagnostic Results:');
      debugPrint('  Status: ${diagnostic['status']}');
      debugPrint('  Issues: ${diagnostic['issues']}');
      debugPrint('  Products loaded: ${diagnostic['products_loaded']}');
      debugPrint('  User: ${diagnostic['user_email']}');

      return diagnostic;
    } catch (e) {
      diagnostic['status'] = 'ERROR';
      diagnostic['issues'].add('Diagnostic failed: $e');
      return diagnostic;
    }
  }

  /// Debug method to manually test content purchase save (for testing only)
  Future<void> debugTestContentPurchase({
    required String contentId,
    required String contentTitle,
    required String contentType,
  }) async {
    try {
      debugPrint('🧪 ========== DEBUG: TESTING CONTENT PURCHASE ==========');
      debugPrint('🧪 Content ID: $contentId');
      debugPrint('🧪 Content Title: $contentTitle');
      debugPrint('🧪 Content Type: $contentType');

      final String? userId = _authService.currentUser?.uid;
      if (userId == null) {
        debugPrint('❌ No user logged in for test');
        return;
      }

      final userEmail = _authService.currentUser?.email ?? '';

      // Test database save directly
      await _dbService.saveUserContentPurchase(
        userId,
        userEmail,
        contentId: contentId,
        contentTitle: contentTitle,
        contentType: contentType,
        purchaseDate: DateTime.now(),
        transactionId: 'TEST_${DateTime.now().millisecondsSinceEpoch}',
        price: contentType == 'mrr_course' ? 49.99 : 9.99,
      );

      // Verify the save
      final hasPurchased = await _dbService.hasUserPurchasedContent(
        userId,
        contentId,
      );

      debugPrint('🧪 Test purchase save result: $hasPurchased');
      debugPrint('🧪 ========== DEBUG: TEST COMPLETED ==========');
    } catch (e) {
      debugPrint('❌ Debug test failed: $e');
    }
  }

  /// Force save a purchase directly to database (for debugging purchase issues)
  Future<bool> forceSavePurchase({
    required String contentId,
    required String contentTitle,
    required String contentType,
  }) async {
    try {
      debugPrint('🔧 ========== FORCE SAVING PURCHASE ==========');
      debugPrint('🔧 Content ID: $contentId');
      debugPrint('🔧 Content Title: $contentTitle');
      debugPrint('🔧 Content Type: $contentType');

      final String? userId = _authService.currentUser?.uid;
      if (userId == null) {
        debugPrint('❌ No user logged in');
        return false;
      }

      final userEmail = _authService.currentUser?.email ?? '';

      // Force save to database
      await _dbService.saveUserContentPurchase(
        userId,
        userEmail,
        contentId: contentId,
        contentTitle: contentTitle,
        contentType: contentType,
        purchaseDate: DateTime.now(),
        transactionId: 'FORCE_${DateTime.now().millisecondsSinceEpoch}',
        price: contentType == 'mrr_course' ? 49.99 : 9.99,
      );

      // Verify the save
      final hasPurchased = await _dbService.hasUserPurchasedContent(
        userId,
        contentId,
      );

      debugPrint('🔧 Force save result: $hasPurchased');
      debugPrint('🔧 ========== FORCE SAVE COMPLETED ==========');

      return hasPurchased;
    } catch (e) {
      debugPrint('❌ Force save failed: $e');
      return false;
    }
  }

  /// Debug method to check if purchase stream is working
  Future<void> debugCheckPurchaseStream() async {
    try {
      debugPrint('🧪 ========== CHECKING PURCHASE STREAM ==========');
      debugPrint('🧪 Is payment service initialized? $_isInitialized');
      debugPrint('🧪 Is in-app purchase available? $_isAvailable');
      debugPrint('🧪 Number of loaded products: ${_products.length}');

      for (var product in _products) {
        debugPrint('🧪 Product: ${product.id} - ${product.title}');
      }

      debugPrint('🧪 Current content ID: $_currentPurchaseContentId');
      debugPrint('🧪 Current content title: $_currentPurchaseContentTitle');

      // Check if we have any pending purchases
      debugPrint('🧪 Checking for pending purchases...');
      await _inAppPurchase.restorePurchases();

      debugPrint('🧪 ========== PURCHASE STREAM CHECK COMPLETED ==========');
    } catch (e) {
      debugPrint('❌ Purchase stream check failed: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _subscription.cancel();
    _purchaseUpdatedController.close();
  }
}
